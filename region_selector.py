# -*- coding: utf-8 -*-
"""
屏幕区域选择模块
允许用户通过拖拽在屏幕上选择区域，获取坐标信息
"""

import tkinter as tk
from tkinter import messagebox
import logging
from typing import Tuple, Optional, Callable
import time
from PIL import Image, ImageTk
import mss
import cv2
import numpy as np

class RegionSelector:
    """屏幕区域选择器"""
    
    def __init__(self, callback: Optional[Callable] = None):
        """
        初始化区域选择器
        
        Args:
            callback: 选择完成后的回调函数，接收(x, y, width, height)参数
        """
        self.logger = logging.getLogger(__name__)
        self.callback = callback
        self.root = None
        self.canvas = None
        self.start_x = 0
        self.start_y = 0
        self.current_x = 0
        self.current_y = 0
        self.rect_id = None
        self.is_selecting = False
        self.screenshot = None
        self.selected_region = None
        
    def start_selection(self) -> Optional[Tuple[int, int, int, int]]:
        """
        开始区域选择
        
        Returns:
            选择的区域坐标 (x, y, width, height) 或 None
        """
        try:
            # 获取屏幕截图
            self.screenshot = self._capture_screen()
            if self.screenshot is None:
                messagebox.showerror("错误", "无法获取屏幕截图")
                return None
            
            # 创建全屏选择窗口
            self._create_selection_window()
            
            # 等待用户选择
            self.root.mainloop()
            
            return self.selected_region
            
        except Exception as e:
            self.logger.error(f"区域选择失败: {str(e)}")
            messagebox.showerror("错误", f"区域选择失败: {str(e)}")
            return None
    
    def _capture_screen(self) -> Optional[Image.Image]:
        """
        捕获屏幕截图
        
        Returns:
            PIL图像对象或None
        """
        try:
            with mss.mss() as sct:
                # 获取主显示器信息
                monitor = sct.monitors[1]  # monitors[0]是所有显示器的总和
                
                # 截取整个屏幕
                screenshot = sct.grab(monitor)
                
                # 转换为PIL Image
                pil_image = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
                
                self.logger.info(f"屏幕截图尺寸: {pil_image.size}")
                return pil_image
                
        except Exception as e:
            self.logger.error(f"屏幕截图失败: {str(e)}")
            return None
    
    def _create_selection_window(self):
        """创建选择窗口"""
        # 创建顶级窗口
        self.root = tk.Toplevel()
        self.root.title("区域选择 - 拖拽选择多空资金区域")
        
        # 设置窗口属性
        self.root.attributes('-topmost', True)  # 置顶
        self.root.attributes('-fullscreen', True)  # 全屏
        self.root.attributes('-alpha', 0.3)  # 设置窗口透明度
        self.root.configure(background='black')
        self.root.bind('<Escape>', self._cancel_selection)  # ESC键取消
        
        # 创建画布
        self.canvas = tk.Canvas(
            self.root,
            highlightthickness=0,
            cursor="crosshair"
        )
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 不显示截图遮罩，保持透明以便看到底层内容
        
        # 绑定鼠标事件
        self.canvas.bind('<Button-1>', self._on_mouse_down)
        self.canvas.bind('<B1-Motion>', self._on_mouse_drag)
        self.canvas.bind('<ButtonRelease-1>', self._on_mouse_up)
        
        # 添加提示文本
        self._add_instructions()
        
        # 使窗口获得焦点
        self.root.focus_force()
    
    def _display_screenshot(self):
        """在画布上显示截图（透明模式）"""
        try:
            # 获取画布尺寸
            self.root.update_idletasks()
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            # 不显示截图，只创建透明画布以供绘制选择框
            # 这样用户可以直接看到下层的指南针软件界面
            self.logger.info(f"创建透明选择画布，尺寸: {canvas_width}x{canvas_height}")
            
        except Exception as e:
            self.logger.error(f"创建透明画布失败: {str(e)}")
    
    def _add_instructions(self):
        """添加操作说明"""
        # 延迟到画布尺寸确定后添加说明
        self.root.after(100, self._delayed_add_instructions)
    
    def _delayed_add_instructions(self):
        """延迟添加操作说明"""
        instructions = [
            "拖拽鼠标选择多空资金数据区域",
            "选择完成后松开鼠标",
            "按 ESC 键取消选择"
        ]
        
        # 获取实际画布尺寸
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        y_offset = 50
        for instruction in instructions:
            self.canvas.create_text(
                canvas_width // 2, y_offset,
                text=instruction,
                fill='yellow',
                font=('Arial', 16, 'bold'),
                tags='instructions'
            )
            y_offset += 35
    
    def _on_mouse_down(self, event):
        """鼠标按下事件"""
        self.start_x = event.x
        self.start_y = event.y
        self.current_x = event.x
        self.current_y = event.y
        self.is_selecting = True
        
        # 删除之前的选择框
        if self.rect_id:
            self.canvas.delete(self.rect_id)
    
    def _on_mouse_drag(self, event):
        """鼠标拖拽事件"""
        if not self.is_selecting:
            return
            
        self.current_x = event.x
        self.current_y = event.y
        
        # 删除之前的选择框
        if self.rect_id:
            self.canvas.delete(self.rect_id)
        
        # 绘制新的选择框（加粗红色边框，更明显）
        self.rect_id = self.canvas.create_rectangle(
            self.start_x, self.start_y,
            self.current_x, self.current_y,
            outline='red', width=3, tags='selection'
        )
        
        # 显示当前选择的坐标信息
        self._update_coordinate_display()
    
    def _on_mouse_up(self, event):
        """鼠标释放事件"""
        if not self.is_selecting:
            return
            
        self.current_x = event.x
        self.current_y = event.y
        self.is_selecting = False
        
        # 计算选择区域
        x = min(self.start_x, self.current_x)
        y = min(self.start_y, self.current_y)
        width = abs(self.current_x - self.start_x)
        height = abs(self.current_y - self.start_y)
        
        # 检查选择区域大小
        if width < 10 or height < 10:
            messagebox.showwarning("警告", "选择区域太小，请重新选择")
            return
        
        # 转换为实际屏幕坐标
        self.selected_region = self._convert_to_screen_coordinates(x, y, width, height)
        
        # 直接调用回调函数，无需确认
        if self.callback and self.selected_region:
            self.callback(*self.selected_region)
        
        # 关闭选择窗口
        self._close_selection()
    
    def _convert_to_screen_coordinates(self, x: int, y: int, width: int, height: int) -> Tuple[int, int, int, int]:
        """
        将画布坐标转换为实际屏幕坐标
        
        Args:
            x, y, width, height: 画布坐标
            
        Returns:
            实际屏幕坐标
        """
        # 在透明模式下，画布坐标即为屏幕坐标（全屏显示）
        # 直接返回原始坐标
        return (x, y, width, height)
    
    def _update_coordinate_display(self):
        """更新坐标显示"""
        if not self.is_selecting:
            return
            
        # 删除之前的坐标显示
        self.canvas.delete('coordinates')
        
        # 计算当前选择区域
        x = min(self.start_x, self.current_x)
        y = min(self.start_y, self.current_y)
        width = abs(self.current_x - self.start_x)
        height = abs(self.current_y - self.start_y)
        
        # 转换为屏幕坐标
        screen_coords = self._convert_to_screen_coordinates(x, y, width, height)
        
        # 显示坐标信息（带背景框增强可读性）
        coord_text = f"区域: ({screen_coords[0]}, {screen_coords[1]}) 尺寸: {screen_coords[2]}×{screen_coords[3]}"
        
        # 创建背景矩形
        text_x = x + width // 2
        text_y = y - 30
        self.canvas.create_rectangle(
            text_x - 150, text_y - 15,
            text_x + 150, text_y + 15,
            fill='black', outline='yellow', width=1,
            tags='coordinates'
        )
        
        # 创建文字
        self.canvas.create_text(
            text_x, text_y,
            text=coord_text,
            fill='yellow',
            font=('Arial', 12, 'bold'),
            tags='coordinates'
        )
    
    def _close_selection(self):
        """关闭选择窗口"""
        try:
            if self.root and self.root.winfo_exists():
                self.root.destroy()
        except Exception as e:
            self.logger.error(f"关闭选择窗口时出错: {str(e)}")
    
    def _cancel_selection(self, event=None):
        """取消选择"""
        self.selected_region = None
        self.root.destroy()

def test_region_selector():
    """测试区域选择器"""
    def on_region_selected(x, y, width, height):
        print(f"选择的区域: 位置({x}, {y}), 尺寸({width}×{height})")
        
        # 显示成功消息
        root = tk.Tk()
        root.withdraw()
        messagebox.showinfo(
            "区域选择完成",
            f"已选择区域:\n位置: ({x}, {y})\n尺寸: {width} × {height}\n\n"
            f"此区域坐标已保存，可用于自动识别多空资金数据。"
        )
        root.destroy()
    
    # 创建主窗口
    root = tk.Tk()
    root.title("区域选择器测试")
    root.geometry("400x200")
    root.configure(bg='#f0f0f0')
    
    # 居中显示
    root.update_idletasks()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 400) // 2
    y = (screen_height - 200) // 2
    root.geometry(f"400x200+{x}+{y}")
    
    # 添加说明
    info_label = tk.Label(
        root,
        text="点击下方按钮开始选择多空资金数据区域\n选择完成后坐标将直接填入程序",
        font=('Microsoft YaHei', 12),
        bg='#f0f0f0',
        fg='#2c3e50'
    )
    info_label.pack(pady=30)
    
    # 添加按钮
    test_button = tk.Button(
        root,
        text="🎯 开始选择屏幕区域",
        command=lambda: RegionSelector(on_region_selected).start_selection(),
        font=('Microsoft YaHei', 14, 'bold'),
        bg='#3498db',
        fg='white',
        width=20,
        height=2,
        relief=tk.RAISED,
        bd=3,
        cursor='hand2'
    )
    test_button.pack(pady=20)
    
    root.mainloop()

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行测试
    test_region_selector()