# -*- coding: utf-8 -*-
"""
GUI OCR操作模块
包含所有OCR相关的操作和区域选择功能
"""

import threading
from tkinter import messagebox

from config import COMPASS_SOFTWARE, update_ocr_region_config, APP_CONFIG
from region_selector import RegionSelector
from compass_automator import CompassAutomator


class GUIOCROperationsMixin:
    """GUI OCR操作Mixin类"""
    
    def load_saved_region(self):
        """读取配置文件中保存的区域信息"""
        try:
            if COMPASS_SOFTWARE.get('ocr_region'):
                region = COMPASS_SOFTWARE['ocr_region']
                x, y, width, height = region['x'], region['y'], region['width'], region['height']
                
                # 检查区域配置是否有效（非默认值）
                if not (x == 100 and y == 200 and width == 300 and height == 100):
                    self.selected_region = (x, y, width, height)
                    self.logger.info(f"加载已保存的区域配置: ({x}, {y}) 尺寸: {width}×{height}")
                    return True
                    
        except Exception as e:
            self.logger.error(f"读取区域配置失败: {str(e)}")
            
        return False
    
    def init_ocr_early(self):
        """程序启动时提前初始化OCR模块"""
        try:
            # 从配置获取OCR设置
            ocr_config = getattr(APP_CONFIG, 'ocr_settings', {})
            use_gpu = ocr_config.get('use_gpu', True)
            debug_mode = ocr_config.get('debug_mode', False)
            save_debug_images = ocr_config.get('save_debug_images', False)
            
            # 使用OCR管理器初始化
            if self.ocr_manager.initialize_ocr(use_gpu, debug_mode, save_debug_images):
                status = self.ocr_manager.get_engine_status()
                available_engines = [name for name, available in status['available_engines'].items() if available]
                self.logger.info(f"OCR模块提前初始化成功，可用引擎: {', '.join(available_engines)}")
                return True
            else:
                self.logger.error("OCR模块初始化失败：无可用引擎")
                return False
                
        except Exception as e:
            self.logger.error(f"OCR模块提前初始化失败: {str(e)}")
            return False
    
    def update_ui_after_init(self):
        """程序初始化后更新界面状态"""
        # 更新OCR状态显示
        if self.ocr_manager.is_initialized():
            self._update_ocr_status()
        
        # 如果有已保存的区域，更新界面显示
        if self.selected_region:
            x, y, width, height = self.selected_region
            region_text = f"区域: ({x}, {y}) 尺寸: {width}×{height}"
            self.region_info_var.set(region_text)
            
            self.logger.info(f"界面已更新，显示保存的区域配置: {region_text}")
        else:
            self.region_info_var.set("未选择区域")
        
    def select_screen_region(self):
        """选择屏幕区域"""
        try:
            self.logger.info("开始选择屏幕区域...")
            
            # 创建区域选择器并设置回调
            selector = RegionSelector(callback=self.on_region_selected)
            
            # 开始选择
            region = selector.start_selection()
            
            if region:
                self.selected_region = region
                self.logger.info(f"区域选择完成: {region}")
            else:
                self.logger.info("用户取消了区域选择")
                
        except Exception as e:
            self.logger.error(f"区域选择失败: {str(e)}")
            messagebox.showerror("错误", f"区域选择失败: {str(e)}")
    
    def on_region_selected(self, x: int, y: int, width: int, height: int):
        """区域选择完成回调"""
        self.selected_region = (x, y, width, height)
        
        # 更新区域信息显示
        region_text = f"区域: ({x}, {y}) 尺寸: {width}×{height}"
        self.region_info_var.set(region_text)
        
        self.logger.info(f"选择区域: {region_text}")
        
        # 保存区域配置到配置文件
        try:
            if update_ocr_region_config(x, y, width, height):
                self.logger.info("区域配置已保存到配置文件")
            else:
                self.logger.warning("保存区域配置到配置文件失败")
        except Exception as e:
            self.logger.error(f"保存区域配置失败: {str(e)}")
        
        # 由于OCR已在程序启动时初始化，这里不再需要初始化
        # 只需要更新OCR状态显示
        if self.ocr_manager.is_initialized():
            self._update_ocr_status()
    
    def run_ocr_diagnostics(self):
        """运行OCR诊断"""
        try:
            self.logger.info("开始OCR诊断...")
            
            def diagnose_thread():
                try:
                    from ocr_diagnostics import OCRDiagnostics
                    
                    # 运行诊断
                    diagnostics = OCRDiagnostics()
                    report = diagnostics.run_full_diagnostics()
                    
                    # 通过消息队列返回结果
                    self.message_queue.put(("ocr_diagnosis_result", report))
                    
                except Exception as e:
                    self.message_queue.put(("ocr_diagnosis_error", str(e)))
            
            threading.Thread(target=diagnose_thread, daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"OCR诊断失败: {str(e)}")
            messagebox.showerror("错误", f"OCR诊断失败: {str(e)}")
    
    def test_fund_ocr_recognition(self):
        """测试多空资金OCR识别"""
        if not self.selected_region:
            messagebox.showwarning("警告", "请先选择屏幕区域")
            return
        
        if not self.ocr_manager.is_initialized():
            messagebox.showerror("错误", "OCR引擎未初始化")
            return
        
        try:
            self.logger.info("开始测试多空资金OCR识别...")
            
            # 检查OCR引擎状态
            status = self.ocr_manager.get_engine_status()
            available_engines = [name for name, available in status['available_engines'].items() if available]
            
            if not available_engines:
                messagebox.showerror("错误", "没有可用的OCR引擎，请运行OCR诊断检查问题")
                return
            
            # 更新状态显示
            self.ocr_status_label.config(text="OCR状态: 测试多空资金识别中...", foreground="blue")
            
            # 在新线程中执行OCR测试以避免阻塞GUI
            def test_fund_thread():
                try:
                    x, y, width, height = self.selected_region
                    
                    # 重置错误计数
                    self.ocr_manager.reset_error_count()
                    
                    # 获取OCR引擎并测试
                    ocr_engine = self.ocr_manager.get_optimized_ocr_engine()
                    if ocr_engine:
                        if hasattr(ocr_engine, 'test_fund_data_ocr_recognition_optimized'):
                            result = ocr_engine.test_fund_data_ocr_recognition_optimized(x, y, width, height)
                        else:
                            result = ocr_engine.test_fund_data_ocr_recognition(x, y, width, height)
                        # 通过消息队列返回结果
                        self.message_queue.put(("fund_ocr_test_result", result))
                    else:
                        self.message_queue.put(("fund_ocr_test_error", "无法获取OCR引擎"))
                    
                except Exception as e:
                    self.message_queue.put(("fund_ocr_test_error", str(e)))
                finally:
                    # 重新启用按钮
                    self.message_queue.put(("fund_ocr_test_complete", None))
            
            threading.Thread(target=test_fund_thread, daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"多空资金OCR测试失败: {str(e)}")
            messagebox.showerror("错误", f"多空资金OCR测试失败: {str(e)}")
            self._update_ocr_status()
    
    def test_ocr_recognition(self):
        """测试OCR识别"""
        if not self.selected_region:
            messagebox.showwarning("警告", "请先选择屏幕区域")
            return
        
        if not self.ocr_manager.is_initialized():
            messagebox.showerror("错误", "OCR引擎未初始化")
            return
        
        try:
            self.logger.info("开始测试OCR识别...")
            
            # 检查OCR引擎状态
            status = self.ocr_manager.get_engine_status()
            available_engines = [name for name, available in status['available_engines'].items() if available]
            
            if not available_engines:
                messagebox.showerror("错误", "没有可用的OCR引擎，请运行OCR诊断检查问题")
                return
            
            # 更新状态显示
            self.ocr_status_label.config(text="OCR状态: 测试中...", foreground="blue")
            
            # 在新线程中执行OCR测试以避免阻塞GUI
            def test_thread():
                try:
                    x, y, width, height = self.selected_region
                    
                    # 重置错误计数
                    self.ocr_manager.reset_error_count()
                    
                    # 获取OCR引擎并测试
                    fund_ocr = self.ocr_manager.get_ocr_engine()
                    if fund_ocr:
                        result = fund_ocr.test_raw_ocr_recognition(x, y, width, height)
                        # 通过消息队列返回结果
                        self.message_queue.put(("ocr_raw_test_result", result))
                    else:
                        self.message_queue.put(("ocr_test_error", "无法获取OCR引擎"))
                    
                except Exception as e:
                    self.message_queue.put(("ocr_test_error", str(e)))
                finally:
                    # 重新启用按钮
                    self.message_queue.put(("ocr_test_complete", None))
            
            threading.Thread(target=test_thread, daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"OCR测试失败: {str(e)}")
            messagebox.showerror("错误", f"OCR测试失败: {str(e)}")
            self._update_ocr_status()
    
    def _update_ocr_status(self):
        """更新OCR状态显示"""
        if self.ocr_manager.is_initialized():
            status = self.ocr_manager.get_engine_status()
            available_engines = [name for name, available in status['available_engines'].items() if available]
            error_count = status.get('error_count', 0)
            
            if available_engines:
                if error_count > 0:
                    status_text = f"OCR状态: 运行中 ({', '.join(available_engines)}) - 错误:{error_count}"
                    status_color = "orange"
                else:
                    status_text = f"OCR状态: 就绪 ({', '.join(available_engines)})"
                    status_color = "green"
            else:
                status_text = "OCR状态: 无可用引擎"
                status_color = "red"
                
            self.ocr_status_label.config(text=status_text, foreground=status_color)
        else:
            self.ocr_status_label.config(text="OCR状态: 未初始化", foreground="orange")
            
            
    def test_connection(self):
        """测试指南针连接"""
        def test_thread():
            try:
                self.message_queue.put(("status", "正在测试连接..."))
                self.message_queue.put(("progress", 50))
                
                automator = CompassAutomator()
                success = automator.start_compass_software()
                
                if success:
                    self.message_queue.put(("log", "指南针连接测试成功"))
                    self.message_queue.put(("status", "连接测试成功"))
                    automator.close_compass_software()
                else:
                    self.message_queue.put(("log", "指南针连接测试失败"))
                    self.message_queue.put(("status", "连接测试失败"))
                    
            except Exception as e:
                self.message_queue.put(("log", f"连接测试错误: {str(e)}"))
                self.message_queue.put(("status", "连接测试错误"))
            finally:
                self.message_queue.put(("progress", 0))
                
        threading.Thread(target=test_thread, daemon=True).start()